/* Change Password Component Styles - Black & White Minimalist Theme */

.password-container {
  width: 100%;
  padding: var(--spacing-lg);
  background-color: var(--bg-main-content);
  min-height: 100vh;
}

/* Unified Profile Card */
.unified-profile-card {
  width: 100%;

  .main-profile-card {
    background: #ffffff; /* White card background to match user management */
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid #e5e7eb;
    overflow: hidden;
  }
}

/* Profile Header Section */
.profile-header-section {
  padding: var(--spacing-xl);

  .profile-info-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);

        .profile-avatar {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #000000;
          color: white;
          font-weight: 600;
          font-size: 1.5rem;
          flex-shrink: 0;
          border: 3px solid #f3f4f6;

          &.has-image {
            background-color: transparent;
            border: 3px solid #e5e7eb;
          }

          .avatar-image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
          }

          .avatar-initials {
            font-size: 1.5rem;
            font-weight: 600;
            color: white;
          }
        }

        .profile-details {
          flex: 1;

          .profile-name {
            margin: 0 0 var(--spacing-xs) 0;
            font-size: 1.75rem;
            font-weight: 600;
            color: #000000;
            line-height: var(--line-height-tight);
          }

          .profile-role {
            margin: 0 0 var(--spacing-xs) 0;
            font-size: 1rem;
            font-weight: 500;
            color: #6b7280;
          }

          .profile-email {
            margin: 0;
            font-size: 0.875rem;
            color: #9ca3af;
          }
        }
      }
    }
  }
}

/* Tab Navigation Section */
.tab-navigation-section {
  border-top: 1px solid #e5e7eb;

  .profile-tabs {
    background: transparent; /* Inherit from parent card */
    border-radius: 0;
    box-shadow: none;
    border: none;
    overflow: hidden;

    ::ng-deep {
      .mat-mdc-tab-group {
        .mat-mdc-tab-header {
          background-color: #f9fafb;
          border-bottom: 1px solid #e5e7eb;

          .mat-mdc-tab-label-container {
            /* Make tabs narrower and left-aligned */
            justify-content: flex-start;
            max-width: 400px; /* Limit tab container width */

            .mat-mdc-tab-label {
              color: #6b7280;
              font-weight: 500;
              min-width: 100px; /* Narrower tabs */
              max-width: 140px; /* Limit individual tab width */
              flex: 0 0 auto; /* Don't grow to fill space */

              &.mdc-tab--active {
                color: #000000;
                font-weight: 600;
              }
            }

            .mat-mdc-tab-label-active {
              color: #000000;
            }
          }

          .mat-ink-bar {
            background-color: #000000;
            height: 3px;
          }
        }

        .mat-mdc-tab-body-wrapper {
          .mat-mdc-tab-body {
            .mat-mdc-tab-body-content {
              padding: 0;
            }
          }
        }
      }
    }
  }

  .tab-content {
    /* Unified tab content styling */
    padding: var(--spacing-xl);
    background: transparent; /* Inherit from parent card */

    .form-card {
      /* Remove card styling to merge with tab content */
      padding: 0;
      background: transparent;
      border: none;
      box-shadow: none;
      margin: 0;

      ::ng-deep {
        .mat-mdc-card-header {
          padding-bottom: var(--spacing-lg);

          .mat-mdc-card-title {
            color: #000000;
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
          }

          .mat-mdc-card-subtitle {
            color: #6b7280;
            font-size: 0.875rem;
            margin: var(--spacing-xs) 0 0 0;
          }
        }

        .mat-mdc-card-content {
          padding: 0;
        }
      }
    }
  }
}

/* Password Form Styles */
.password-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);

  .form-field {
    width: 100%;

    ::ng-deep {
      .mat-mdc-form-field {
        .mat-mdc-text-field-wrapper {
          .mat-mdc-form-field-flex {
            .mat-mdc-form-field-outline {
              color: #d1d5db;

              .mat-mdc-form-field-outline-thick {
                color: #000000;
              }
            }

            .mat-mdc-form-field-infix {
              .mat-mdc-input-element {
                color: #000000;
                font-size: 0.875rem;

                &::placeholder {
                  color: #9ca3af;
                }
              }
            }
          }
        }

        .mat-mdc-form-field-label {
          color: #6b7280;
        }

        .mat-mdc-form-field-hint {
          color: #9ca3af;
          font-size: 0.75rem;
        }

        .mat-mdc-form-field-error {
          color: #ef4444;
          font-size: 0.75rem;
        }
      }

      .mat-icon {
        color: #9ca3af;
      }

      .mat-icon-button {
        &:hover {
          background-color: rgba(0, 0, 0, 0.04);
        }
      }
    }
  }
}

/* Password Requirements */
.password-requirements {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin: var(--spacing-md) 0;

  h4 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
  }

  ul {
    margin: 0;
    padding-left: var(--spacing-lg);
    list-style-type: disc;

    li {
      font-size: 0.75rem;
      color: #6b7280;
      margin-bottom: var(--spacing-xs);

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

/* Action Buttons */
.form-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  margin-top: var(--spacing-lg);

  .cancel-button {
    background: #000000; /* Black background */
    color: white; /* White text */
    border: 2px solid white; /* White thick border */
    font-weight: 500;
    padding: 0 var(--spacing-lg);
    min-width: 100px;

    &:hover:not([disabled]) {
      background-color: #333333;
    }

    &[disabled] {
      background: #666666;
      color: white;
      border-color: white;
    }
  }

  .submit-button {
    background: #000000; /* Black background */
    color: white; /* White text */
    border: 2px solid white; /* White thick border */
    font-weight: 500;
    padding: 0 var(--spacing-lg);
    min-width: 140px;
    position: relative;

    &:hover:not([disabled]) {
      background-color: #333333;
      border-color: white;
    }

    &[disabled] {
      background: #666666;
      color: white;
      border-color: white;
    }

    .button-spinner {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);

      ::ng-deep {
        circle {
          stroke: white;
        }
      }
    }

    .hidden {
      visibility: hidden;
    }
  }
}

/* Profile Redirect Section */
.profile-redirect {
  text-align: center;
  padding: var(--spacing-xl);

  p {
    color: #6b7280;
    margin-bottom: var(--spacing-lg);
    font-size: 0.875rem;
  }
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: var(--spacing-md);

  p {
    color: #6b7280;
    font-size: 0.875rem;
  }

  ::ng-deep {
    .mat-mdc-progress-spinner {
      circle {
        stroke: #000000;
      }
    }
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .password-container {
    background: var(--bg-main-content); /* Light grayish-white background */
  }

  .unified-profile-card .main-profile-card {
    background: #ffffff; /* White card */
    border-color: #e0e0e0;
    // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .tab-navigation-section {
    border-top-color: #e5e7eb;
  }

  .profile-header-section .profile-info-section {
    .profile-details {
      .profile-name {
        color: #333333;
      }

      .profile-role {
        color: #666666;
      }

      .profile-email {
        color: #666666;
      }
    }
  }

  .tab-navigation-section .profile-tabs ::ng-deep {
    .mat-mdc-tab-group .mat-mdc-tab-header {
      background-color: #f9fafb;
      border-bottom-color: #e5e7eb;

      .mat-mdc-tab-label-container .mat-mdc-tab-label {
        color: #6b7280;

        &.mdc-tab--active {
          color: #000000;
        }
      }

      .mat-ink-bar {
        background-color: #000000;
      }
    }
  }

  .tab-content .form-card ::ng-deep {
    .mat-mdc-card-header {
      .mat-mdc-card-title {
        color: #000000;
      }

      .mat-mdc-card-subtitle {
        color: #6b7280;
      }
    }
  }
}

/* Dark Theme Support */
:host-context(.dark-theme) {
  .password-container {
    background: var(--bg-main-content); /* Keep main background consistent */
  }

  .unified-profile-card .main-profile-card {
    background: #2a2a2a; /* Greyish background for dark mode */
    border-color: #404040;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .tab-navigation-section {
    border-top-color: #404040;
  }

  .profile-header-section .profile-info-section {
    .profile-details {
      .profile-name {
        color: white;
      }

      .profile-role {
        color: #d1d5db;
      }

      .profile-email {
        color: #9ca3af;
      }
    }
  }

  .tab-navigation-section .profile-tabs ::ng-deep {
    .mat-mdc-tab-group .mat-mdc-tab-header {
      background-color: #374151;
      border-bottom-color: #4b5563;

      .mat-mdc-tab-label-container .mat-mdc-tab-label {
        color: #d1d5db;

        &.mdc-tab--active {
          color: white;
        }
      }

      .mat-ink-bar {
        background-color: white;
      }
    }
  }

  .tab-content .form-card ::ng-deep {
    .mat-mdc-card-header {
      .mat-mdc-card-title {
        color: white;
      }

      .mat-mdc-card-subtitle {
        color: #d1d5db;
      }
    }
  }

  .password-form .form-field ::ng-deep {
    .mat-mdc-form-field {
      .mat-mdc-text-field-wrapper .mat-mdc-form-field-flex {
        .mat-mdc-form-field-outline {
          color: #4b5563;

          .mat-mdc-form-field-outline-thick {
            color: white;
          }
        }

        .mat-mdc-form-field-infix .mat-mdc-input-element {
          color: white;
        }
      }

      .mat-mdc-form-field-label {
        color: #d1d5db;
      }
    }

    .mat-icon {
      color: #d1d5db;
    }

    .mat-icon-button:hover {
      background-color: rgba(255, 255, 255, 0.08);
    }
  }

  .password-requirements {
    background-color: #374151;
    border-color: #4b5563;

    h4 {
      color: #f3f4f6;
    }

    ul li {
      color: #d1d5db;
    }
  }

  .form-actions {
    .submit-button {
      background: white;
      color: #000000;
      border-color: white;

      &:hover:not([disabled]) {
        background-color: #f3f4f6;
      }
    }
  }

  .profile-redirect p {
    color: #d1d5db;
  }

  .loading-container {
    p {
      color: #d1d5db;
    }

    ::ng-deep {
      .mat-mdc-progress-spinner circle {
        stroke: white;
      }
    }
  }
  :host ::ng-deep .form-field {
    .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #404040;
      }
    }

    &.mat-focused .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #10b981;
        border-width: 2px;
      }
    }

    &:hover:not(.mat-focused) .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #505050;
      }
    }

    .mat-mdc-form-field-label {
      color: #b0b0b0 !important;
    }

    .mat-mdc-floating-label {
      color: #b0b0b0 !important;
    }

    &.mat-focused .mat-mdc-floating-label {
      color: #10b981 !important;
    }

    .mat-mdc-input-element,
    .mat-mdc-select-trigger {
      color: #ffffff;
    }

    .mat-mdc-select-arrow {
      color: #b0b0b0;
    }

    &.mat-form-field-invalid {
      .mat-mdc-form-field-outline {
        .mdc-notched-outline__leading,
        .mdc-notched-outline__notch,
        .mdc-notched-outline__trailing {
          border-color: #f44336;
        }
      }
    }

    .mat-mdc-form-field-error {
      color: #f44336;
    }
  }
}

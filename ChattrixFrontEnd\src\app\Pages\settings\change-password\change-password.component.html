<!-- Change Password Page Container -->
<div class="password-container" *ngIf="userProfile$ | async as userProfile">
  <!-- Profile Header Section -->
  <div class="profile-header">
    <mat-card class="header-card">
      <div class="header-content">
        <!-- Profile Picture and Basic Info -->
        <div class="profile-info-section">
          <!-- Integrated Photo Management in Header -->
          <div
            class="profile-avatar clickable"
            [class.has-image]="imagePreview || userProfile.profilePictureUrl"
            (click)="triggerFileInput($event)"
            (keydown.enter)="triggerFileInput($event)"
            (keydown.space)="triggerFileInput($event)"
            tabindex="0"
            role="button"
            [attr.aria-label]="
              imagePreview || userProfile.profilePictureUrl
                ? 'Change profile picture'
                : 'Upload profile picture'
            "
            [matTooltip]="
              imagePreview || userProfile.profilePictureUrl
                ? 'Click to change photo'
                : 'Click to upload photo'
            "
            matTooltipPosition="below"
          >
            <!-- Preview or current image -->
            <img
              *ngIf="imagePreview"
              [src]="imagePreview"
              alt="Profile Preview"
              class="avatar-image"
            />
            <img
              *ngIf="!imagePreview && userProfile.profilePictureUrl"
              [src]="userProfile.profilePictureUrl"
              [alt]="getUserDisplayName(userProfile)"
              class="avatar-image"
              #avatarImg
              (error)="avatarImg.style.display = 'none'"
            />
            <!-- Initials when no image -->
            <span
              *ngIf="!imagePreview && !userProfile.profilePictureUrl"
              class="avatar-initials"
            >
              {{ getUserInitials(userProfile) }}
            </span>
            <!-- Hover overlay -->
            <div
              *ngIf="imagePreview || userProfile.profilePictureUrl"
              class="avatar-overlay"
            >
              <mat-icon class="edit-icon">edit</mat-icon>
              <span class="edit-text">Change</span>
            </div>
            <div
              *ngIf="!imagePreview && !userProfile.profilePictureUrl"
              class="avatar-overlay"
            >
              <mat-icon class="upload-icon">add_a_photo</mat-icon>
              <span class="upload-text">Add Photo</span>
            </div>
            <!-- Remove button -->
            <button
              *ngIf="imagePreview || userProfile.profilePictureUrl"
              type="button"
              mat-icon-button
              class="remove-avatar-btn"
              (click)="removeImage($event)"
              aria-label="Remove image"
              matTooltip="Remove photo"
              matTooltipPosition="right"
            >
              <mat-icon>close</mat-icon>
            </button>
          </div>

          <!-- Hidden file input -->
          <input
            type="file"
            id="profileFileInput"
            accept="image/jpeg,image/jpg,image/png"
            (change)="onFileSelected($event)"
            style="display: none"
          />

          <div class="profile-details">
            <div class="profile-name-section">
              <h1 class="profile-name">
                {{ getUserDisplayName(userProfile) }}
              </h1>
              <span class="role-badge">{{ getRoleDisplay(userProfile) }}</span>
            </div>
            <p class="profile-email">{{ userProfile.email }}</p>
          </div>
        </div>
      </div>
    </mat-card>
  </div>

  <!-- Tab Navigation -->
  <div class="tab-navigation">
    <mat-tab-group
      [selectedIndex]="selectedTabIndex"
      (selectedTabChange)="onTabChange($event.index)"
      class="profile-tabs"
    >
      <!-- Profile Info Tab -->
      <mat-tab label="Personal Info">
        <div class="tab-content">
          <mat-card class="form-card">
            <mat-card-header>
              <mat-card-title>Personal Information</mat-card-title>
              <mat-card-subtitle
                >Update your personal details</mat-card-subtitle
              >
            </mat-card-header>

            <mat-card-content>
              <div class="profile-redirect">
                <p>
                  Click the button below to update your personal information.
                </p>
                <button
                  mat-raised-button
                  class="submit-button"
                  (click)="onTabChange(0)"
                >
                  Edit Profile
                </button>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>

      <!-- Change Password Tab -->
      <mat-tab label="Change Password">
        <div class="tab-content">
          <mat-card class="form-card">
            <mat-card-header>
              <mat-card-title>Change Password</mat-card-title>
              <mat-card-subtitle
                >Update your account password securely</mat-card-subtitle
              >
            </mat-card-header>

            <mat-card-content>
              <form
                [formGroup]="passwordForm"
                (ngSubmit)="onSubmitPassword()"
                class="password-form"
              >
                <!-- Current Password Field -->
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Current password</mat-label>
                  <input
                    matInput
                    [type]="showCurrentPassword ? 'text' : 'password'"
                    formControlName="currentPassword"
                    placeholder="Enter your current password"
                    autocomplete="current-password"
                  />
                  <button
                    type="button"
                    mat-icon-button
                    matSuffix
                    (click)="togglePasswordVisibility('current')"
                    [attr.aria-label]="'Hide password'"
                    [attr.aria-pressed]="showCurrentPassword"
                  >
                    <mat-icon>{{
                      showCurrentPassword ? "visibility_off" : "visibility"
                    }}</mat-icon>
                  </button>
                  <mat-error *ngIf="getFieldError('currentPassword')">
                    {{ getFieldError("currentPassword") }}
                  </mat-error>
                </mat-form-field>

                <!-- New Password Field -->
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>New password</mat-label>
                  <input
                    matInput
                    [type]="showNewPassword ? 'text' : 'password'"
                    formControlName="newPassword"
                    placeholder="Enter your new password"
                    autocomplete="new-password"
                  />
                  <button
                    type="button"
                    mat-icon-button
                    matSuffix
                    (click)="togglePasswordVisibility('new')"
                    [attr.aria-label]="'Hide password'"
                    [attr.aria-pressed]="showNewPassword"
                  >
                    <mat-icon>{{
                      showNewPassword ? "visibility_off" : "visibility"
                    }}</mat-icon>
                  </button>
                  <mat-error *ngIf="getFieldError('newPassword')">
                    {{ getFieldError("newPassword") }}
                  </mat-error>
                  <mat-hint *ngIf="getPasswordRequirements().length > 0">
                    Missing: {{ getPasswordRequirements().join(", ") }}
                  </mat-hint>
                </mat-form-field>

                <!-- Confirm New Password Field -->
                <mat-form-field appearance="outline" class="form-field">
                  <mat-label>Confirm new password</mat-label>
                  <input
                    matInput
                    [type]="showConfirmPassword ? 'text' : 'password'"
                    formControlName="confirmPassword"
                    placeholder="Confirm your new password"
                    autocomplete="new-password"
                  />
                  <button
                    type="button"
                    mat-icon-button
                    matSuffix
                    (click)="togglePasswordVisibility('confirm')"
                    [attr.aria-label]="'Hide password'"
                    [attr.aria-pressed]="showConfirmPassword"
                  >
                    <mat-icon>{{
                      showConfirmPassword ? "visibility_off" : "visibility"
                    }}</mat-icon>
                  </button>
                  <mat-error *ngIf="getFieldError('confirmPassword')">
                    {{ getFieldError("confirmPassword") }}
                  </mat-error>
                </mat-form-field>

                <!-- Password Requirements Info -->
                <div class="password-requirements">
                  <h4>Password Requirements:</h4>
                  <ul>
                    <li>At least 8 characters long</li>
                    <li>Contains at least one uppercase letter</li>
                    <li>Contains at least one lowercase letter</li>
                    <li>Contains at least one number</li>
                    <li>
                      Contains at least one special character (!&#64;#$%^&*)
                    </li>
                  </ul>
                </div>

                <!-- Action Buttons -->
                <div class="form-actions">
                  <button
                    type="button"
                    mat-stroked-button
                    class="cancel-button"
                    (click)="onCancelPassword()"
                    [disabled]="isSubmitting"
                  >
                    Cancel
                  </button>

                  <button
                    type="submit"
                    mat-raised-button
                    class="submit-button"
                    [disabled]="passwordForm.invalid || isSubmitting"
                  >
                    <mat-spinner
                      *ngIf="isSubmitting"
                      diameter="20"
                      class="button-spinner"
                    ></mat-spinner>
                    <span [class.hidden]="isSubmitting">Update Password</span>
                  </button>
                </div>
              </form>
            </mat-card-content>
          </mat-card>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>

<!-- Loading State -->
<div *ngIf="!(userProfile$ | async)" class="loading-container">
  <mat-spinner diameter="50"></mat-spinner>
  <p>Loading profile...</p>
</div>

import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { UserDetails } from '../../../Pages/user-management/Models/UserManagement';
import { UserProfileService } from '../../../Pages/chattrix/Services/UserProfile.service';

export interface StatusToggleDialogData {
  user: UserDetails;
  currentUserRoles: string[];
}

@Component({
  selector: 'app-status-toggle-dialog',
  standalone: false,
  templateUrl: './status-toggle-dialog.component.html',
  styleUrl: './status-toggle-dialog.component.scss',
})
export class StatusToggleDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<StatusToggleDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: StatusToggleDialogData,
    private userProfileService: UserProfileService,
  ) {}

  get actionText(): string {
    return this.data.user.isActive ? 'deactivate' : 'activate';
  }

  get actionIcon(): string {
    return this.data.user.isActive ? 'block' : 'check_circle';
  }

  get actionClass(): string {
    return this.data.user.isActive ? 'deactivate' : 'activate';
  }

  getUserDisplayName(): string {
    return this.data.user.fullName || this.data.user.email || 'Unknown User';
  }

  getUserRoleDisplay(): string {
    if (Array.isArray(this.data.user.roles)) {
      return this.data.user.roles.join(', ');
    }
    return (this.data.user.roles as string) || 'User';
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  /**
   * Get profile picture URL for a user
   */
  getProfilePictureUrl(user: UserDetails): string | undefined {
    if (!user.profileImageUrl) {
      return undefined;
    }

    // If it's already a full URL, return as is
    if (user.profileImageUrl.startsWith('http')) {
      return user.profileImageUrl;
    }

    // Otherwise, construct the full S3 URL using UserProfileService
    return this.userProfileService.getS3Url(user.profileImageUrl);
  }

  /**
   * Get user initials for avatar fallback
   */
  getUserInitials(user: UserDetails): string {
    if (user.fullName) {
      const names = user.fullName.split(' ').filter((name) => name.length > 0);
      if (names.length >= 2) {
        return (names[0][0] + names[1][0]).toUpperCase();
      } else if (names.length === 1) {
        return names[0].substring(0, 2).toUpperCase();
      }
    }
    return user.email?.charAt(0).toUpperCase() || 'U';
  }

  onConfirm(): void {
    this.dialogRef.close({
      confirmed: true,
      user: this.data.user,
      newStatus: !this.data.user.isActive,
    });
  }
}

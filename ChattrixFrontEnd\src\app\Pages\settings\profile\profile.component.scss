/* Profile Component Styles - Black & White Minimalist Theme */

.profile-container {
  width: 100%;
  padding: var(--spacing-lg);
  background-color: var(--bg-main-content);
  min-height: 100vh;
}

/* Profile Header Section */
.profile-header {
  margin-bottom: var(--spacing-xl);

  .header-card {
    background: #ffffff; /* White card background to match user management */
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid #e5e7eb;

    .header-content {
      padding: var(--spacing-xl);

      .profile-info-section {
        display: flex;
        align-items: center;
        gap: var(--spacing-lg);

        .profile-avatar {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #000000;
          color: white;
          font-weight: 600;
          font-size: 1.5rem;
          flex-shrink: 0;
          border: 3px solid #f3f4f6;

          &.has-image {
            background-color: transparent;
            border: 3px solid #e5e7eb;
          }

          .avatar-image {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
          }

          .avatar-initials {
            font-size: 1.5rem;
            font-weight: 600;
            color: white;
          }
        }

        .profile-details {
          flex: 1;

          .profile-name {
            margin: 0 0 var(--spacing-xs) 0;
            font-size: 1.75rem;
            font-weight: 600;
            color: #000000;
            line-height: var(--line-height-tight);
          }

          .profile-role {
            margin: 0 0 var(--spacing-xs) 0;
            font-size: 1rem;
            font-weight: 500;
            color: #6b7280;
          }

          .profile-email {
            margin: 0;
            font-size: 0.875rem;
            color: #9ca3af;
          }
        }
      }
    }
  }
}

/* Tab Navigation */
.tab-navigation {
  .profile-tabs {
    background: #ffffff; /* White card background to match user management */
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid #e5e7eb;
    overflow: hidden;

    ::ng-deep {
      .mat-mdc-tab-group {
        .mat-mdc-tab-header {
          background-color: #f9fafb;
          border-bottom: 1px solid #e5e7eb;

          .mat-mdc-tab-label-container {
            .mat-mdc-tab-label {
              color: #6b7280;
              font-weight: 500;
              min-width: 120px;

              &.mdc-tab--active {
                color: #000000;
                font-weight: 600;
              }
            }

            .mat-mdc-tab-label-active {
              color: #000000;
            }
          }

          .mat-ink-bar {
            background-color: #000000;
            height: 3px;
          }
        }

        .mat-mdc-tab-body-wrapper {
          .mat-mdc-tab-body {
            .mat-mdc-tab-body-content {
              padding: 0;
            }
          }
        }
      }
    }
  }

  .tab-content {
    padding: var(--spacing-lg);

    .form-card {
      padding: var(--spacing-lg);
      background: #ffffff; /* White card background to match user management */
      border: none;
      box-shadow: none;
      margin: 0;

      .form-card-header {
        padding: var(--spacing-lg) 0;
      }
      ::ng-deep {
        .mat-mdc-card-header {
          padding-bottom: var(--spacing-lg);

          .mat-mdc-card-title {
            color: #000000;
            font-size: 1.25rem;
            font-weight: 600;
            margin: 0;
          }

          .mat-mdc-card-subtitle {
            color: #6b7280;
            font-size: 0.875rem;
            margin: var(--spacing-xs) 0 0 0;
          }
        }

        .mat-mdc-card-content {
          padding: 0;
        }
      }
    }
  }
}

/* Profile Picture Section */
.profile-picture-section {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid #e5e7eb;

  .section-title {
    color: #000000;
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0 0 var(--spacing-md) 0;
  }

  .profile-picture-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);

    .profile-picture-preview {
      position: relative;
      width: 120px;
      height: 120px;
      border-radius: 50%;
      border: 3px solid #e5e7eb;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #f9fafb;
      cursor: pointer;
      transition: all 0.3s ease;
      overflow: hidden;

      &:hover {
        border-color: #000000;
        transform: scale(1.02);
      }

      &:focus {
        outline: 2px solid #000000;
        outline-offset: 2px;
      }

      &.has-image {
        border-color: #000000;
        background-color: transparent;
      }

      .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
      }

      .placeholder-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: var(--spacing-xs);
        color: #6b7280;

        .upload-icon {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
        }

        .upload-text {
          font-size: 0.875rem;
          font-weight: 500;
        }
      }

      .overlay-content {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: var(--spacing-xs);
        color: white;
        opacity: 0;
        transition: opacity 0.3s ease;

        .change-icon {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }

        .change-text {
          font-size: 0.875rem;
          font-weight: 500;
        }
      }

      &:hover .overlay-content {
        opacity: 1;
      }

      .remove-image-btn {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #ef4444;
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        &:hover {
          background: #dc2626;
        }

        mat-icon {
          font-size: 18px;
          width: 18px;
          height: 18px;
        }
      }
    }

    .upload-hint {
      color: #6b7280;
      font-size: 0.75rem;
      text-align: center;
      margin: 0;
    }
  }
}

/* Form Styles */
.profile-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);

  .form-field {
    width: 100%;

    ::ng-deep {
      .mat-mdc-form-field {
        .mat-mdc-text-field-wrapper {
          .mat-mdc-form-field-flex {
            .mat-mdc-form-field-outline {
              color: #d1d5db;

              .mat-mdc-form-field-outline-thick {
                color: #000000;
              }
            }

            .mat-mdc-form-field-infix {
              .mat-mdc-input-element {
                color: #000000;
                font-size: 0.875rem;

                &::placeholder {
                  color: #9ca3af;
                }

                &.readonly-field {
                  background-color: #f9fafb;
                  color: #6b7280;
                  cursor: not-allowed;
                }
              }
            }
          }
        }

        .mat-mdc-form-field-label {
          color: #6b7280;
        }

        .mat-mdc-form-field-hint {
          color: #9ca3af;
          font-size: 0.75rem;
        }

        .mat-mdc-form-field-error {
          color: #ef4444;
          font-size: 0.75rem;
        }
      }

      .mat-icon {
        color: #9ca3af;
      }
    }

    &.description-field {
      ::ng-deep {
        .mat-mdc-form-field {
          .mat-mdc-text-field-wrapper {
            .mat-mdc-form-field-flex {
              .mat-mdc-form-field-infix {
                textarea.mat-mdc-input-element {
                  resize: vertical;
                  min-height: 100px;
                }
              }
            }
          }
        }
      }
    }
  }
}

/* Action Buttons */
.form-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  margin-top: var(--spacing-lg);

  .cancel-button {
    background: #000000; /* Black background */
    color: white; /* White text */
    border: 2px solid white; /* White thick border */
    font-weight: 500;
    padding: 0 var(--spacing-lg);
    min-width: 100px;

    &:hover:not([disabled]) {
      background-color: #333333;
    }

    &[disabled] {
      background: #666666;
      color: white;
      border-color: white;
    }
  }

  .submit-button {
    background: #000000; /* Black background */
    color: white; /* White text */
    border: 2px solid white; /* White thick border */
    font-weight: 500;
    padding: 0 var(--spacing-lg);
    min-width: 140px;
    position: relative;

    &:hover:not([disabled]) {
      background-color: #333333;
      border-color: white;
    }

    &[disabled] {
      background: #666666;
      color: white;
      border-color: white;
    }

    .button-spinner {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);

      ::ng-deep {
        circle {
          stroke: white;
        }
      }
    }

    .hidden {
      visibility: hidden;
    }
  }
}

/* Password Redirect Section */
.password-redirect {
  text-align: center;
  padding: var(--spacing-xl);

  p {
    color: #6b7280;
    margin-bottom: var(--spacing-lg);
    font-size: 0.875rem;
  }
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: var(--spacing-md);

  p {
    color: #6b7280;
    font-size: 0.875rem;
  }

  ::ng-deep {
    .mat-mdc-progress-spinner {
      circle {
        stroke: #000000;
      }
    }
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .profile-container {
    background: var(--bg-main-content); /* Light grayish-white background */
  }

  .profile-header .header-card,
  .tab-navigation .profile-tabs,
  .tab-content .form-card {
    background: #ffffff; /* White card */
    border-color: #e0e0e0;
    // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .profile-header .header-card .header-content .profile-info-section {
    .profile-details {
      .profile-name {
        color: #333333;
      }

      .profile-role {
        color: #666666;
      }

      .profile-email {
        color: #666666;
      }
    }
  }

  .tab-navigation .profile-tabs ::ng-deep {
    .mat-mdc-tab-group .mat-mdc-tab-header {
      background-color: #f9fafb;
      border-bottom-color: #e5e7eb;

      .mat-mdc-tab-label-container .mat-mdc-tab-label {
        color: #6b7280;

        &.mdc-tab--active {
          color: #000000;
        }
      }

      .mat-ink-bar {
        background-color: #000000;
      }
    }
  }

  .tab-content .form-card ::ng-deep {
    .mat-mdc-card-header {
      .mat-mdc-card-title {
        color: #000000;
      }

      .mat-mdc-card-subtitle {
        color: #6b7280;
      }
    }
  }

  /* Profile Picture Section Light Theme */
  .profile-picture-section {
    border-bottom-color: #e5e7eb;

    .section-title {
      color: #000000;
    }

    .profile-picture-container {
      .profile-picture-preview {
        border-color: #e5e7eb;
        background-color: #f9fafb;

        &:hover {
          border-color: #000000;
        }

        &:focus {
          outline-color: #000000;
        }

        &.has-image {
          border-color: #000000;
        }

        .placeholder-content {
          color: #6b7280;
        }
      }

      .upload-hint {
        color: #6b7280;
      }
    }
  }
}

/* Dark Theme Support */
:host-context(.dark-theme) {
  .profile-container {
    background: var(--bg-main-content); /* Keep main background consistent */
  }

  .profile-header .header-card,
  .tab-navigation .profile-tabs,
  .tab-content .form-card {
    background: #2a2a2a; /* Greyish background for dark mode */
    border-color: #404040;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .profile-header .header-card .header-content .profile-info-section {
    .profile-details {
      .profile-name {
        color: white;
      }

      .profile-role {
        color: #d1d5db;
      }

      .profile-email {
        color: #9ca3af;
      }
    }
  }

  .form-actions {
    .submit-button {
      background: white;
      color: #000000;
      border-color: white;

      &:hover:not([disabled]) {
        background-color: #f3f4f6;
      }
    }
  }

  .tab-navigation .profile-tabs ::ng-deep {
    .mat-mdc-tab-group .mat-mdc-tab-header {
      background-color: #374151;
      border-bottom-color: #4b5563;

      .mat-mdc-tab-label-container .mat-mdc-tab-label {
        color: #d1d5db;

        &.mdc-tab--active {
          color: white;
        }
      }

      .mat-ink-bar {
        background-color: white;
      }
    }
  }

  .tab-content .form-card ::ng-deep {
    .mat-mdc-card-header {
      .mat-mdc-card-title {
        color: white;
      }

      .mat-mdc-card-subtitle {
        color: #d1d5db;
      }
    }
  }

  /* Profile Picture Section Dark Theme */
  .profile-picture-section {
    border-bottom-color: #4b5563;

    .section-title {
      color: white;
    }

    .profile-picture-container {
      .profile-picture-preview {
        border-color: #4b5563;
        background-color: #374151;

        &:hover {
          border-color: white;
        }

        &:focus {
          outline-color: white;
        }

        &.has-image {
          border-color: white;
        }

        .placeholder-content {
          color: #d1d5db;
        }
      }

      .upload-hint {
        color: #d1d5db;
      }
    }
  }

  :host ::ng-deep .form-field {
    .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #404040;
      }
    }

    &.mat-focused .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #10b981;
        border-width: 2px;
      }
    }

    &:hover:not(.mat-focused) .mat-mdc-form-field-outline {
      .mdc-notched-outline__leading,
      .mdc-notched-outline__notch,
      .mdc-notched-outline__trailing {
        border-color: #505050;
      }
    }

    .mat-mdc-form-field-label {
      color: #b0b0b0 !important;
    }

    .mat-mdc-floating-label {
      color: #b0b0b0 !important;
    }

    &.mat-focused .mat-mdc-floating-label {
      color: #10b981 !important;
    }

    .mat-mdc-input-element,
    .mat-mdc-select-trigger {
      color: #ffffff;
    }

    .mat-mdc-select-arrow {
      color: #b0b0b0;
    }

    &.mat-form-field-invalid {
      .mat-mdc-form-field-outline {
        .mdc-notched-outline__leading,
        .mdc-notched-outline__notch,
        .mdc-notched-outline__trailing {
          border-color: #f44336;
        }
      }
    }

    .mat-mdc-form-field-error {
      color: #f44336;
    }
  }

  .password-redirect p {
    color: #d1d5db;
  }

  .loading-container {
    p {
      color: #d1d5db;
    }

    ::ng-deep {
      .mat-mdc-progress-spinner circle {
        stroke: white;
      }
    }
  }
}
